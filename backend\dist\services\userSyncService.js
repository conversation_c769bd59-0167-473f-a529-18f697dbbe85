"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSyncService = void 0;
const client_1 = require("@prisma/client");
const backend_1 = require("@clerk/backend");
const prisma = new client_1.PrismaClient();
const clerkClient = (0, backend_1.createClerkClient)({ secretKey: process.env.CLERK_SECRET_KEY });
class UserSyncService {
    static async syncUserFromClerk(clerkUserId) {
        try {
            const clerkUser = await clerkClient.users.getUser(clerkUserId);
            if (!clerkUser) {
                throw new Error(`User not found in Clerk: ${clerkUserId}`);
            }
            let dbUser = await prisma.user.findUnique({
                where: { clerkId: clerkUserId }
            });
            const userData = {
                clerkId: clerkUser.id,
                email: clerkUser.emailAddresses[0]?.emailAddress || '',
                firstName: clerkUser.firstName || '',
                lastName: clerkUser.lastName || '',
                username: clerkUser.username || clerkUser.emailAddresses[0]?.emailAddress?.split('@')[0],
                role: clerkUser.publicMetadata?.role || 'CLIENT',
                status: 'ACTIVE',
                phone: clerkUser.phoneNumbers[0]?.phoneNumber || null,
            };
            if (dbUser) {
                dbUser = await prisma.user.update({
                    where: { clerkId: clerkUserId },
                    data: {
                        ...userData,
                        updatedAt: new Date(),
                    }
                });
            }
            else {
                dbUser = await prisma.user.create({
                    data: userData
                });
                await this.createUserProfile(dbUser);
            }
            return dbUser;
        }
        catch (error) {
            console.error('Error syncing user from Clerk:', error);
            throw error;
        }
    }
    static async createUserProfile(user) {
        try {
            switch (user.role) {
                case 'CLIENT':
                    await prisma.clientProfile.create({
                        data: {
                            userId: user.id,
                            companyName: '',
                            contactPerson: `${user.firstName} ${user.lastName}`.trim(),
                        }
                    });
                    break;
                case 'AGENT':
                case 'SUPERVISOR':
                    await prisma.agentProfile.create({
                        data: {
                            userId: user.id,
                            employeeId: `EMP-${Date.now()}`,
                            hireDate: new Date(),
                            skills: [],
                            certifications: [],
                        }
                    });
                    break;
                case 'ADMIN':
                    await prisma.adminProfile.create({
                        data: {
                            userId: user.id,
                            permissions: ['users.read', 'reports.read'],
                            accessLevel: 'STANDARD',
                        }
                    });
                    break;
            }
        }
        catch (error) {
            console.error('Error creating user profile:', error);
            throw error;
        }
    }
    static async syncAllUsersFromClerk() {
        let synced = 0;
        let errors = 0;
        let offset = 0;
        const limit = 100;
        try {
            while (true) {
                const usersResponse = await clerkClient.users.getUserList({
                    limit,
                    offset
                });
                const users = usersResponse.data || usersResponse;
                if (!users || users.length === 0)
                    break;
                for (const clerkUser of users) {
                    try {
                        await this.syncUserFromClerk(clerkUser.id);
                        synced++;
                    }
                    catch (error) {
                        console.error(`Failed to sync user ${clerkUser.id}:`, error);
                        errors++;
                    }
                }
                offset += limit;
                if (users.length < limit)
                    break;
            }
            console.log(`User sync completed: ${synced} synced, ${errors} errors`);
            return { synced, errors };
        }
        catch (error) {
            console.error('Error during bulk user sync:', error);
            throw error;
        }
    }
    static async getUserWithSync(clerkUserId) {
        try {
            let user = await prisma.user.findUnique({
                where: { clerkId: clerkUserId },
                include: {
                    clientProfile: true,
                    agentProfile: true,
                    adminProfile: true,
                }
            });
            if (!user) {
                console.log(`User ${clerkUserId} not found in database, syncing from Clerk...`);
                await this.syncUserFromClerk(clerkUserId);
                user = await prisma.user.findUnique({
                    where: { clerkId: clerkUserId },
                    include: {
                        clientProfile: true,
                        agentProfile: true,
                        adminProfile: true,
                    }
                });
            }
            return user;
        }
        catch (error) {
            console.error('Error getting user with sync:', error);
            throw error;
        }
    }
    static async createAdminUser(adminData) {
        try {
            const clerkUser = await clerkClient.users.createUser({
                emailAddress: [adminData.email],
                firstName: adminData.firstName,
                lastName: adminData.lastName,
                password: adminData.password,
                publicMetadata: {
                    role: 'ADMIN',
                    permissions: adminData.permissions || ['users.read', 'users.write', 'reports.read', 'reports.write'],
                    accessLevel: adminData.accessLevel || 'FULL'
                }
            });
            const dbUser = await prisma.user.create({
                data: {
                    clerkId: clerkUser.id,
                    email: adminData.email,
                    firstName: adminData.firstName,
                    lastName: adminData.lastName,
                    username: adminData.email.split('@')[0],
                    role: 'ADMIN',
                    status: 'ACTIVE',
                }
            });
            await prisma.adminProfile.create({
                data: {
                    userId: dbUser.id,
                    permissions: adminData.permissions || ['users.read', 'users.write', 'reports.read', 'reports.write'],
                    accessLevel: adminData.accessLevel || 'FULL',
                }
            });
            console.log('Admin user created successfully:', dbUser.id);
            return dbUser;
        }
        catch (error) {
            console.error('Error creating admin user:', error);
            throw error;
        }
    }
}
exports.UserSyncService = UserSyncService;
//# sourceMappingURL=userSyncService.js.map