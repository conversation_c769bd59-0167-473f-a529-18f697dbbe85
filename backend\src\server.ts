import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { PrismaClient } from '@prisma/client';

// Import route handlers
import usersRouter from './routes/users';
import adminUsersRouter from './routes/admin-users';
import shiftsRouter from './routes/shifts';
import sitesRouter from './routes/sites';
import reportsRouter from './routes/reports';
import analyticsRouter from './routes/analytics';
import clientPortalRouter from './routes/client-portal';
import clientRouter from './routes/client';
import webhooksRouter from './routes/webhooks';
import authTestRouter from './routes/auth-test';

// Import middleware
import { requireAuth, requireAdmin, requireClient, optionalAuth, handleAuthError } from './middleware/auth';

// Load environment variables
dotenv.config();

// Initialize Prisma client
const prisma = new PrismaClient();

const app = express();
const server = createServer(app);

const PORT = process.env.PORT || 8000;

// Test route - should be the first route registered
app.get('/test-route', (req, res) => {
  console.log('Test route hit!');
  res.json({ success: true, message: 'Test route is working!' });
});

// CORS Configuration
const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      'http://localhost:3000', // Client portal (unified deployment root)
      'http://localhost:3001', // Admin portal
      'http://localhost:3002', // Legacy client portal port
      'http://localhost:3003', // Additional development port
      'https://bahinlink.vercel.app', // Production deployment
      process.env.CORS_ORIGIN
    ].filter(Boolean);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`Blocked request from unauthorized origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  optionsSuccessStatus: 200
};

// Middleware
app.use(cors(corsOptions));
app.options('*', cors(corsOptions)); // Enable pre-flight for all routes
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  });
});

// API routes
console.log('Registering API routes...');

app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    services: {
      database: 'healthy',
      redis: 'healthy',
      websocket: 'healthy'
    },
    timestamp: new Date().toISOString()
  });
});

// Register API route handlers (temporarily commented out for testing)
console.log('Setting up basic test routes...');

// Basic test routes
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Backend API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'Test endpoint working',
    data: {
      server: 'BahinLink Backend',
      status: 'operational'
    }
  });
});

// Test database connection endpoint
app.get('/api/test-db', async (req, res) => {
  try {
    const { PrismaClient } = await import('@prisma/client');
    const testPrisma = new PrismaClient();

    // Test database connection
    const userCount = await testPrisma.user.count();
    const adminCount = await testPrisma.user.count({ where: { role: 'ADMIN' } });

    res.json({
      success: true,
      message: 'Database connection successful',
      data: {
        totalUsers: userCount,
        adminUsers: adminCount,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Database test error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Database connection failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    });
  }
});

console.log('✅ Basic test routes registered successfully');

// Register webhook routes (before other middleware)
app.use('/api/webhooks', webhooksRouter);

// Register API routes
console.log('Registering main API routes...');
app.use('/api/users', requireAuth, usersRouter);
app.use('/api/admin-users', requireAuth, requireAdmin, adminUsersRouter);
app.use('/api/shifts', requireAuth, shiftsRouter);
app.use('/api/sites', requireAuth, sitesRouter);
app.use('/api/reports', requireAuth, reportsRouter);
app.use('/api/analytics', requireAuth, analyticsRouter);
app.use('/api/client-portal', requireAuth, clientPortalRouter);
app.use('/api/client', requireAuth, clientRouter);
app.use('/api/auth-test', authTestRouter); // Keep test routes unprotected for testing

// Add test analytics endpoint without authentication
app.get('/api/test-analytics', async (req, res) => {
  console.log('Test analytics endpoint hit - no auth required');
  try {
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();

    const startDate = new Date(new Date().setDate(new Date().getDate() - 30));
    const endDate = new Date();

    // Get current active shifts
    const activeShifts = await prisma.shift.count({
      where: {
        status: 'IN_PROGRESS'
      }
    });

    // Get total agents
    const totalAgents = await prisma.agentProfile.count();

    // Get incidents today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const incidentsToday = await prisma.report.count({
      where: {
        type: 'INCIDENT',
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    // Get sites monitored
    const sitesMonitored = await prisma.site.count({
      where: {
        status: 'ACTIVE'
      }
    });

    const dashboardData = {
      overview: {
        activeShifts,
        totalAgents,
        incidentsToday,
        sitesMonitored,
        completionRate: 85.5,
        responseTime: 12.3
      },
      message: 'Test endpoint working - authentication bypassed'
    };

    res.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in test analytics endpoint:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch test analytics data'
      }
    });
  }
});

console.log('✅ All API routes registered successfully');





// WebSocket will be added later

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An internal server error occurred'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: 'Endpoint not found'
    }
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 BahinLink Backend API is running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 API endpoints: http://localhost:${PORT}/api`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

export { app, server };
