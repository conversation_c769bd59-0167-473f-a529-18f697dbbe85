import express from 'express';
import { PrismaClient } from '@prisma/client';
import { query, validationResult } from 'express-validator';

const router = express.Router();
const prisma = new PrismaClient();

// Middleware to handle validation errors
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Invalid input data',
        details: errors.array()
      }
    });
  }
  next();
};

// GET /api/analytics/dashboard - Get dashboard analytics
router.get('/dashboard', [
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601(),
  query('clientId').optional().isUUID(),
], handleValidationErrors, async (req, res) => {
  console.log('Analytics dashboard endpoint hit');
  console.log('Headers:', req.headers);
  console.log('Auth header:', req.headers.authorization);
  try {
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date(new Date().setDate(new Date().getDate() - 30));
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date();
    const clientId = req.query.clientId as string;

    // Build base where clause
    const baseWhere: any = {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    };

    if (clientId) {
      baseWhere.site = {
        clientId: clientId
      };
    }

    // Get current active shifts
    const activeShifts = await prisma.shift.count({
      where: {
        status: 'IN_PROGRESS',
        ...(clientId && {
          site: {
            clientId: clientId
          }
        })
      }
    });

    // Get total agents
    const totalAgents = await prisma.agentProfile.count();

    // Get incidents today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const incidentsToday = await prisma.report.count({
      where: {
        type: 'INCIDENT',
        createdAt: {
          gte: today,
          lt: tomorrow
        },

        ...(clientId && {
          site: {
            clientId: clientId
          }
        })
      }
    });

    // Get sites monitored
    const sitesMonitored = await prisma.site.count({
      where: {
        status: 'ACTIVE',

        ...(clientId && { clientId: clientId })
      }
    });

    // Calculate completion rate (completed shifts vs total shifts)
    const [completedShifts, totalShifts] = await Promise.all([
      prisma.shift.count({
        where: {
          ...baseWhere,
          status: 'COMPLETED'
        }
      }),
      prisma.shift.count({
        where: {
          ...baseWhere,
          status: {
            in: ['COMPLETED', 'CANCELLED', 'NO_SHOW']
          }
        }
      })
    ]);

    const completionRate = totalShifts > 0 ? (completedShifts / totalShifts) * 100 : 0;

    // Calculate average response time for incidents
    const incidents = await prisma.report.findMany({
      where: {
        ...baseWhere,
        type: 'INCIDENT',
        status: {
          in: ['APPROVED', 'REVIEWED']
        }
      },
      select: {
        createdAt: true,
        updatedAt: true
      }
    });

    const responseTime = incidents.length > 0 
      ? incidents.reduce((acc, incident) => {
          const diff = incident.updatedAt.getTime() - incident.createdAt.getTime();
          return acc + (diff / (1000 * 60)); // Convert to minutes
        }, 0) / incidents.length
      : 0;

    // Get shift trends (last 7 days)
    const shiftTrends = await Promise.all(
      Array.from({ length: 7 }, async (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        date.setHours(0, 0, 0, 0);
        const nextDate = new Date(date);
        nextDate.setDate(nextDate.getDate() + 1);

        const count = await prisma.shift.count({
          where: {
            startTime: {
              gte: date,
              lt: nextDate
            },

            ...(clientId && {
              site: {
                clientId: clientId
              }
            })
          }
        });

        return {
          date: date.toISOString().split('T')[0],
          shifts: count
        };
      })
    );

    // Get incident trends by priority
    const incidentsByPriority = await prisma.report.groupBy({
      by: ['priority'],
      where: {
        ...baseWhere,
        type: 'INCIDENT'
      },
      _count: {
        priority: true
      }
    });

    // Get top performing agents
    const topAgents = await prisma.agentProfile.findMany({
      where: {

      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true
          }
        },
        shifts: {
          where: {
            ...baseWhere,
            status: 'COMPLETED'
          }
        },
        _count: {
          select: {
            shifts: {
              where: {
                ...baseWhere,
                status: 'COMPLETED'
              }
            }
          }
        }
      },
      orderBy: {
        shifts: {
          _count: 'desc'
        }
      },
      take: 5
    });

    // Get recent activities
    const recentActivities = await prisma.report.findMany({
      where: {

        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        },
        ...(clientId && {
          site: {
            clientId: clientId
          }
        })
      },
      include: {
        author: {
          select: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    const dashboardData = {
      overview: {
        activeShifts,
        totalAgents,
        incidentsToday,
        sitesMonitored,
        completionRate: Math.round(completionRate * 100) / 100,
        responseTime: Math.round(responseTime * 100) / 100
      },
      trends: {
        shifts: shiftTrends.reverse(),
        incidentsByPriority: incidentsByPriority.map(item => ({
          priority: item.priority,
          count: item._count.priority
        }))
      },
      topAgents: topAgents.map(agent => ({
        id: agent.id,
        name: `${agent.user.firstName} ${agent.user.lastName}`,
        completedShifts: 0, // Simplified for now
        employeeId: agent.employeeId
      })),
      recentActivities: recentActivities.map(activity => ({
        id: activity.id,
        type: activity.type,
        title: activity.title,
        priority: activity.priority,
        agentName: `${activity.author.user.firstName} ${activity.author.user.lastName}`,
        siteName: activity.site?.name || 'Unknown Site',
        createdAt: activity.createdAt
      }))
    };

    res.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching dashboard analytics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch dashboard analytics'
      }
    });
  }
});

// GET /api/analytics/performance - Get performance metrics
router.get('/performance', [
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601(),
  query('agentId').optional().isUUID(),
  query('siteId').optional().isUUID(),
], handleValidationErrors, async (req, res) => {
  try {
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date(new Date().setDate(new Date().getDate() - 30));
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date();
    const agentId = req.query.agentId as string;
    const siteId = req.query.siteId as string;

    const baseWhere: any = {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    };

    if (agentId) baseWhere.agentId = agentId;
    if (siteId) baseWhere.siteId = siteId;

    // Get shift performance metrics
    const shiftMetrics = await prisma.shift.groupBy({
      by: ['status'],
      where: baseWhere,
      _count: {
        status: true
      }
    });

    // Get report metrics
    const reportMetrics = await prisma.report.groupBy({
      by: ['type', 'status'],
      where: baseWhere,
      _count: {
        type: true
      }
    });

    // Get attendance metrics - temporarily disabled
    const attendanceMetrics = []; // await prisma.attendance.groupBy({
    //   by: ['status'],
    //   where: {
    //     createdAt: {
    //       gte: startDate,
    //       lte: endDate
    //     },
    //     ...(agentId && { agentId }),
    //     ...(siteId && { siteId })
    //   },
    //   _count: {
    //     status: true
    //   }
    // });

    res.json({
      success: true,
      data: {
        shifts: shiftMetrics,
        reports: reportMetrics,
        attendance: attendanceMetrics,
        period: {
          startDate,
          endDate
        }
      }
    });
  } catch (error) {
    console.error('Error fetching performance metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch performance metrics'
      }
    });
  }
});

// Test endpoint without authentication for debugging
router.get('/test-dashboard', async (req, res) => {
  console.log('Test dashboard endpoint hit - no auth required');
  try {
    const startDate = new Date(new Date().setDate(new Date().getDate() - 30));
    const endDate = new Date();

    // Get current active shifts
    const activeShifts = await prisma.shift.count({
      where: {
        status: 'IN_PROGRESS'
      }
    });

    // Get total agents
    const totalAgents = await prisma.agentProfile.count();

    // Get incidents today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const incidentsToday = await prisma.report.count({
      where: {
        type: 'INCIDENT',
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    // Get sites monitored
    const sitesMonitored = await prisma.site.count({
      where: {
        status: 'ACTIVE'
      }
    });

    const dashboardData = {
      overview: {
        activeShifts,
        totalAgents,
        incidentsToday,
        sitesMonitored,
        completionRate: 85.5,
        responseTime: 12.3
      },
      message: 'Test endpoint working - authentication bypassed'
    };

    res.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in test dashboard endpoint:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch test dashboard data'
      }
    });
  }
});

export default router;
