# Production Environment Configuration for Backend

# Database Configuration
DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19qTGVVWXk1ZXZ1NTZvLUFublhqNzIiLCJhcGlfa2V5IjoiMDFKWldXSzJQRFNUV1JZNVcxVjI1TVZKR0oiLCJ0ZW5hbnRfaWQiOiI5ZDA3NjJhYzgxNGEwMTYyNjYyMzFhY2I2MjQ0NmViYzFhYjRlODJkNTk2ZDRlNGE0YzZiY2Y2NGY1MDQyYTRmIiwiaW50ZXJuYWxfc2VjcmV0IjoiMzA5MWY3NTktMDU5Ny00NTBlLTlhYWQtNGY0ZmI1NDM1OTIyIn0.H-twYVvGFtn4TM6wJPFgexRcUfSDyBGrH614Mvcg4Ws"
DATABASE_POOL_SIZE=50
DATABASE_TIMEOUT=60000

# Redis Configuration (Production)
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""
REDIS_DB=0

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-for-production"
JWT_EXPIRES_IN="24h"
JWT_REFRESH_EXPIRES_IN="7d"

# CORS Configuration
CORS_ORIGIN="https://bahinlink.vercel.app"

# Server Configuration
PORT=8000
NODE_ENV=production

# Clerk Configuration (Production)
CLERK_SECRET_KEY=sk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_PUBLISHABLE_KEY=pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk


# Email Configuration (Production)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-production-app-password"

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH="./uploads"

# Logging Configuration
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
ENABLE_FILE_UPLOAD=true
ENABLE_REAL_TIME=true

# External API Configuration
GOOGLE_MAPS_API_KEY="AIzaSyDU76orYVGACDfFLpcXGaJJanCAPk9CPQE"
TWILIO_ACCOUNT_SID="your-production-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-production-twilio-auth-token"
TWILIO_PHONE_NUMBER="your-production-twilio-phone-number"

# Monitoring Configuration
SENTRY_DSN="your-production-sentry-dsn"
NEW_RELIC_LICENSE_KEY="your-production-new-relic-license-key"
