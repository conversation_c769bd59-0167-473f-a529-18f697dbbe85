# Backend Environment Configuration for Localhost Nginx Setup
# Copy this to .env when using the unified localhost configuration

# Server Configuration
PORT=8000
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost

# Database Configuration (update as needed)
DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19qTGVVWXk1ZXZ1NTZvLUFublhqNzIiLCJhcGlfa2V5IjoiMDFKWldXSzJQRFNUV1JZNVcxVjI1TVZKR0oiLCJ0ZW5hbnRfaWQiOiI5ZDA3NjJhYzgxNGEwMTYyNjYyMzFhY2I2MjQ0NmViYzFhYjRlODJkNTk2ZDRlNGE0YzZiY2Y2NGY1MDQyYTRmIiwiaW50ZXJuYWxfc2VjcmV0IjoiMzA5MWY3NTktMDU5Ny00NTBlLTlhYWQtNGY0ZmI1NDM1OTIyIn0.H-twYVvGFtn4TM6wJPFgexRcUfSDyBGrH614Mvcg4Ws"
# Redis Configuration (if using)
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRES_IN=24h

# Clerk Configuration (if using)
CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key
CLERK_SECRET_KEY=your-clerk-secret-key

# File Upload Configuration
UPLOAD_MAX_SIZE=50mb
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=combined

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
HELMET_ENABLED=true
TRUST_PROXY=true

# WebSocket Configuration
WEBSOCKET_ENABLED=true
WEBSOCKET_PATH=/socket.io

# API Documentation
API_DOCS_ENABLED=true
API_DOCS_PATH=/api-docs