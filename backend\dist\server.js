"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.server = exports.app = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = require("http");
const client_1 = require("@prisma/client");
const users_1 = __importDefault(require("./routes/users"));
const admin_users_1 = __importDefault(require("./routes/admin-users"));
const shifts_1 = __importDefault(require("./routes/shifts"));
const sites_1 = __importDefault(require("./routes/sites"));
const reports_1 = __importDefault(require("./routes/reports"));
const analytics_1 = __importDefault(require("./routes/analytics"));
const client_portal_1 = __importDefault(require("./routes/client-portal"));
const client_2 = __importDefault(require("./routes/client"));
const webhooks_1 = __importDefault(require("./routes/webhooks"));
const auth_test_1 = __importDefault(require("./routes/auth-test"));
const auth_1 = require("./middleware/auth");
dotenv_1.default.config();
const prisma = new client_1.PrismaClient();
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
exports.server = server;
const PORT = process.env.PORT || 8000;
app.get('/test-route', (req, res) => {
    console.log('Test route hit!');
    res.json({ success: true, message: 'Test route is working!' });
});
const corsOptions = {
    origin: (origin, callback) => {
        if (!origin)
            return callback(null, true);
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://localhost:3002',
            'http://localhost:3003',
            'https://bahinlink.vercel.app',
            process.env.CORS_ORIGIN
        ].filter(Boolean);
        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            console.warn(`Blocked request from unauthorized origin: ${origin}`);
            callback(new Error('Not allowed by CORS'));
        }
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
    optionsSuccessStatus: 200
};
app.use((0, cors_1.default)(corsOptions));
app.options('*', (0, cors_1.default)(corsOptions));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0'
    });
});
console.log('Registering API routes...');
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        services: {
            database: 'healthy',
            redis: 'healthy',
            websocket: 'healthy'
        },
        timestamp: new Date().toISOString()
    });
});
console.log('Setting up basic test routes...');
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'Backend API is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});
app.get('/api/test', (req, res) => {
    res.json({
        success: true,
        message: 'Test endpoint working',
        data: {
            server: 'BahinLink Backend',
            status: 'operational'
        }
    });
});
console.log('✅ Basic test routes registered successfully');
app.use('/api/webhooks', webhooks_1.default);
console.log('Registering main API routes...');
app.use('/api/users', auth_1.requireAuth, users_1.default);
app.use('/api/admin-users', auth_1.requireAuth, auth_1.requireAdmin, admin_users_1.default);
app.use('/api/shifts', auth_1.requireAuth, shifts_1.default);
app.use('/api/sites', auth_1.requireAuth, sites_1.default);
app.use('/api/reports', auth_1.requireAuth, reports_1.default);
app.use('/api/analytics', auth_1.requireAuth, analytics_1.default);
app.use('/api/client-portal', auth_1.requireAuth, client_portal_1.default);
app.use('/api/client', auth_1.requireAuth, client_2.default);
app.use('/api/auth-test', auth_test_1.default);
app.get('/api/test-analytics', async (req, res) => {
    console.log('Test analytics endpoint hit - no auth required');
    try {
        const { PrismaClient } = await Promise.resolve().then(() => __importStar(require('@prisma/client')));
        const prisma = new PrismaClient();
        const startDate = new Date(new Date().setDate(new Date().getDate() - 30));
        const endDate = new Date();
        const activeShifts = await prisma.shift.count({
            where: {
                status: 'IN_PROGRESS'
            }
        });
        const totalAgents = await prisma.agentProfile.count();
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const incidentsToday = await prisma.report.count({
            where: {
                type: 'INCIDENT',
                createdAt: {
                    gte: today,
                    lt: tomorrow
                }
            }
        });
        const sitesMonitored = await prisma.site.count({
            where: {
                status: 'ACTIVE'
            }
        });
        const dashboardData = {
            overview: {
                activeShifts,
                totalAgents,
                incidentsToday,
                sitesMonitored,
                completionRate: 85.5,
                responseTime: 12.3
            },
            message: 'Test endpoint working - authentication bypassed'
        };
        res.json({
            success: true,
            data: dashboardData,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error in test analytics endpoint:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch test analytics data'
            }
        });
    }
});
console.log('✅ All API routes registered successfully');
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({
        success: false,
        error: {
            code: 'INTERNAL_SERVER_ERROR',
            message: 'An internal server error occurred'
        }
    });
});
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            code: 'NOT_FOUND',
            message: 'Endpoint not found'
        }
    });
});
server.listen(PORT, () => {
    console.log(`🚀 BahinLink Backend API is running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`🔧 API endpoints: http://localhost:${PORT}/api`);
    console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
});
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
//# sourceMappingURL=server.js.map