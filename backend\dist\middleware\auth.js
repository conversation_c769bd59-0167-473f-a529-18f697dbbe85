"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleAuthError = exports.optionalAuth = exports.requireAgent = exports.requireClient = exports.requireAdmin = exports.requireRole = exports.requireAuth = void 0;
const backend_1 = require("@clerk/backend");
const userSyncService_1 = require("../services/userSyncService");
const client_1 = require("@prisma/client");
const clerkClient = (0, backend_1.createClerkClient)({ secretKey: process.env.CLERK_SECRET_KEY });
const prisma = new client_1.PrismaClient();
const requireAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'AUTHENTICATION_REQUIRED',
                    message: 'Authentication token required'
                }
            });
        }
        const token = authHeader.substring(7);
        if (process.env.NODE_ENV === 'development' && token === 'dev-bypass-token') {
            console.log('Using development bypass token');
            const defaultUser = await prisma.user.findFirst({
                where: { role: 'ADMIN', status: 'ACTIVE' }
            });
            if (defaultUser) {
                req.auth = {
                    userId: defaultUser.clerkId || 'dev-user',
                    sessionId: 'dev-session',
                    claims: { sub: defaultUser.clerkId || 'dev-user' }
                };
                req.user = {
                    id: defaultUser.id,
                    role: defaultUser.role,
                    email: defaultUser.email
                };
                return next();
            }
        }
        let sessionClaims;
        try {
            sessionClaims = await clerkClient.verifyToken(token);
            if (!sessionClaims || !sessionClaims.sub) {
                throw new Error('Invalid token claims');
            }
        }
        catch (error) {
            console.error('Token verification failed:', error);
            return res.status(401).json({
                success: false,
                error: {
                    code: 'INVALID_TOKEN',
                    message: 'Invalid authentication token'
                }
            });
        }
        const userId = sessionClaims.sub;
        const user = await userSyncService_1.UserSyncService.getUserWithSync(userId);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'USER_NOT_FOUND',
                    message: 'User not found'
                }
            });
        }
        req.auth = {
            userId: userId,
            sessionId: sessionClaims.sid || 'unknown',
            claims: sessionClaims
        };
        req.user = {
            id: user.id,
            role: user.role,
            email: user.email
        };
        next();
    }
    catch (error) {
        console.error('Authentication error:', error);
        return res.status(401).json({
            success: false,
            error: {
                code: 'AUTHENTICATION_FAILED',
                message: 'Authentication failed'
            }
        });
    }
};
exports.requireAuth = requireAuth;
const requireRole = (allowedRoles) => {
    return (req, res, next) => {
        if (!req.auth || !req.user) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'AUTHENTICATION_REQUIRED',
                    message: 'Authentication required'
                }
            });
        }
        const userRole = req.user.role;
        if (!allowedRoles.includes(userRole)) {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'INSUFFICIENT_PERMISSIONS',
                    message: `You are currently signed in as a ${userRole.toLowerCase()} user. The admin portal requires administrator or supervisor privileges.`
                }
            });
        }
        next();
    };
};
exports.requireRole = requireRole;
exports.requireAdmin = (0, exports.requireRole)(['ADMIN', 'SUPERVISOR']);
exports.requireClient = (0, exports.requireRole)(['CLIENT']);
exports.requireAgent = (0, exports.requireRole)(['AGENT', 'SUPERVISOR', 'ADMIN']);
const optionalAuth = (req, res, next) => {
    if (process.env.NODE_ENV === 'development') {
        const mockUserId = req.headers['x-user-id'] || 'mock-user-id';
        const mockRole = req.headers['x-user-role'] || 'CLIENT';
        req.auth = {
            userId: mockUserId,
            sessionId: 'mock-session-id',
            claims: {
                email: '<EMAIL>'
            }
        };
        req.user = {
            id: mockUserId,
            role: mockRole,
            email: '<EMAIL>'
        };
    }
    next();
};
exports.optionalAuth = optionalAuth;
const handleAuthError = (error, req, res, next) => {
    if (error.name === 'UnauthorizedError' || error.status === 401) {
        return res.status(401).json({
            success: false,
            error: {
                code: 'AUTHENTICATION_FAILED',
                message: 'Invalid or expired authentication token'
            }
        });
    }
    next(error);
};
exports.handleAuthError = handleAuthError;
//# sourceMappingURL=auth.js.map