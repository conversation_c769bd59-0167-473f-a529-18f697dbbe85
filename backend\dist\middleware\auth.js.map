{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,4CAAmD;AACnD,iEAA8D;AAC9D,2CAA8C;AAqB9C,MAAM,WAAW,GAAG,IAAA,2BAAiB,EAAC,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC;AACnF,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAG3B,MAAM,WAAW,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACnF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,yBAAyB;oBAC/B,OAAO,EAAE,+BAA+B;iBACzC;aACF,CAAC,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAGtC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,KAAK,KAAK,kBAAkB,EAAE,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAG9C,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBAC9C,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE;aAC3C,CAAC,CAAC;YAEH,IAAI,WAAW,EAAE,CAAC;gBAChB,GAAG,CAAC,IAAI,GAAG;oBACT,MAAM,EAAE,WAAW,CAAC,OAAO,IAAI,UAAU;oBACzC,SAAS,EAAE,aAAa;oBACxB,MAAM,EAAE,EAAE,GAAG,EAAE,WAAW,CAAC,OAAO,IAAI,UAAU,EAAE;iBACnD,CAAC;gBAEF,GAAG,CAAC,IAAI,GAAG;oBACT,EAAE,EAAE,WAAW,CAAC,EAAE;oBAClB,IAAI,EAAE,WAAW,CAAC,IAAI;oBACtB,KAAK,EAAE,WAAW,CAAC,KAAK;iBACzB,CAAC;gBAEF,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;QACH,CAAC;QAGD,IAAI,aAAkB,CAAC;QACvB,IAAI,CAAC;YAEH,aAAa,GAAG,MAAO,WAAmB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE9D,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;gBACzC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,8BAA8B;iBACxC;aACF,CAAC,CAAC;QACL,CAAC;QAED,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC;QAGjC,MAAM,IAAI,GAAG,MAAM,iCAAe,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAE3D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,gBAAgB;oBACtB,OAAO,EAAE,gBAAgB;iBAC1B;aACF,CAAC,CAAC;QACL,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG;YACT,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,aAAa,CAAC,GAAG,IAAI,SAAS;YACzC,MAAM,EAAE,aAAa;SACtB,CAAC;QAEF,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC9C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,uBAAuB;aACjC;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AArGW,QAAA,WAAW,eAqGtB;AAcK,MAAM,WAAW,GAAG,CAAC,YAAsB,EAAE,EAAE;IACpD,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YAC3B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,yBAAyB;oBAC/B,OAAO,EAAE,yBAAyB;iBACnC;aACF,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QAE/B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,oCAAoC,QAAQ,CAAC,WAAW,EAAE,0EAA0E;iBAC9I;aACF,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AA3BW,QAAA,WAAW,eA2BtB;AAGW,QAAA,YAAY,GAAG,IAAA,mBAAW,EAAC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;AAGpD,QAAA,aAAa,GAAG,IAAA,mBAAW,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AAGxC,QAAA,YAAY,GAAG,IAAA,mBAAW,EAAC,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;AAGnE,MAAM,YAAY,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAE9E,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW,IAAI,cAAc,CAAC;QACxE,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAW,IAAI,QAAQ,CAAC;QAElE,GAAG,CAAC,IAAI,GAAG;YACT,MAAM,EAAE,UAAU;YAClB,SAAS,EAAE,iBAAiB;YAC5B,MAAM,EAAE;gBACN,KAAK,EAAE,kBAAkB;aAC1B;SACF,CAAC;QAEF,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,kBAAkB;SAC1B,CAAC;IACJ,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAtBW,QAAA,YAAY,gBAsBvB;AAGK,MAAM,eAAe,GAAG,CAAC,KAAU,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC7F,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QAC/D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,yCAAyC;aACnD;SACF,CAAC,CAAC;IACL,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC,CAAC;AAZW,QAAA,eAAe,mBAY1B"}