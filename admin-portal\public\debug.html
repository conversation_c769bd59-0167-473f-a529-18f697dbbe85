<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>🔧 Admin Portal Debug Test</h1>
    
    <div id="status">
        <div class="status info">🔄 Testing basic functionality...</div>
    </div>

    <div id="results"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');

        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            statusDiv.appendChild(div);
        }

        function addResult(message) {
            const div = document.createElement('div');
            div.innerHTML = `<p>${message}</p>`;
            resultsDiv.appendChild(div);
        }

        // Test 1: Basic JavaScript
        try {
            addStatus('✅ JavaScript is working', 'success');
            addResult('✅ Basic JavaScript execution: OK');
        } catch (e) {
            addStatus('❌ JavaScript failed: ' + e.message, 'error');
        }

        // Test 2: Fetch API
        try {
            addResult('✅ Fetch API available: ' + (typeof fetch !== 'undefined' ? 'YES' : 'NO'));
        } catch (e) {
            addResult('❌ Fetch API test failed: ' + e.message);
        }

        // Test 3: Local Storage
        try {
            localStorage.setItem('test', 'value');
            const value = localStorage.getItem('test');
            localStorage.removeItem('test');
            addResult('✅ Local Storage: ' + (value === 'value' ? 'OK' : 'FAILED'));
        } catch (e) {
            addResult('❌ Local Storage failed: ' + e.message);
        }

        // Test 4: Environment Variables (simulated)
        addResult('🔧 Current URL: ' + window.location.href);
        addResult('🔧 User Agent: ' + navigator.userAgent.substring(0, 100) + '...');

        // Test 5: API Connectivity
        async function testAPI() {
            try {
                addResult('🔄 Testing API connectivity...');
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                addStatus('✅ Backend API is reachable', 'success');
                addResult('✅ Backend Health: ' + JSON.stringify(data, null, 2));
            } catch (error) {
                addStatus('❌ Backend API unreachable: ' + error.message, 'error');
                addResult('❌ API Error: ' + error.message);
            }
        }

        // Test 6: React App Status
        setTimeout(() => {
            const reactRoot = document.getElementById('root');
            if (reactRoot) {
                const hasContent = reactRoot.innerHTML.trim().length > 0;
                if (hasContent) {
                    addStatus('✅ React app is loaded', 'success');
                    addResult('✅ React root has content: ' + reactRoot.innerHTML.substring(0, 100) + '...');
                } else {
                    addStatus('❌ React app failed to load (empty root)', 'error');
                    addResult('❌ React root is empty - this explains the white page');
                }
            } else {
                addStatus('❌ React root element not found', 'error');
                addResult('❌ No #root element found in DOM');
            }
        }, 2000);

        // Run API test
        testAPI();

        addStatus('🔍 Debug test completed', 'info');
    </script>
</body>
</html>
