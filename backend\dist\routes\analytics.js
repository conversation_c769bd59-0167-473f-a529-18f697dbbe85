"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const express_validator_1 = require("express-validator");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'VALIDATION_ERROR',
                message: 'Invalid input data',
                details: errors.array()
            }
        });
    }
    next();
};
router.get('/dashboard', [
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601(),
    (0, express_validator_1.query)('clientId').optional().isUUID(),
], handleValidationErrors, async (req, res) => {
    console.log('Analytics dashboard endpoint hit');
    console.log('Headers:', req.headers);
    console.log('Auth header:', req.headers.authorization);
    try {
        const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date(new Date().setDate(new Date().getDate() - 30));
        const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
        const clientId = req.query.clientId;
        const baseWhere = {
            createdAt: {
                gte: startDate,
                lte: endDate
            }
        };
        if (clientId) {
            baseWhere.site = {
                clientId: clientId
            };
        }
        const activeShifts = await prisma.shift.count({
            where: {
                status: 'IN_PROGRESS',
                ...(clientId && {
                    site: {
                        clientId: clientId
                    }
                })
            }
        });
        const totalAgents = await prisma.agentProfile.count();
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const incidentsToday = await prisma.report.count({
            where: {
                type: 'INCIDENT',
                createdAt: {
                    gte: today,
                    lt: tomorrow
                },
                ...(clientId && {
                    site: {
                        clientId: clientId
                    }
                })
            }
        });
        const sitesMonitored = await prisma.site.count({
            where: {
                status: 'ACTIVE',
                ...(clientId && { clientId: clientId })
            }
        });
        const [completedShifts, totalShifts] = await Promise.all([
            prisma.shift.count({
                where: {
                    ...baseWhere,
                    status: 'COMPLETED'
                }
            }),
            prisma.shift.count({
                where: {
                    ...baseWhere,
                    status: {
                        in: ['COMPLETED', 'CANCELLED', 'NO_SHOW']
                    }
                }
            })
        ]);
        const completionRate = totalShifts > 0 ? (completedShifts / totalShifts) * 100 : 0;
        const incidents = await prisma.report.findMany({
            where: {
                ...baseWhere,
                type: 'INCIDENT',
                status: {
                    in: ['APPROVED', 'REVIEWED']
                }
            },
            select: {
                createdAt: true,
                updatedAt: true
            }
        });
        const responseTime = incidents.length > 0
            ? incidents.reduce((acc, incident) => {
                const diff = incident.updatedAt.getTime() - incident.createdAt.getTime();
                return acc + (diff / (1000 * 60));
            }, 0) / incidents.length
            : 0;
        const shiftTrends = await Promise.all(Array.from({ length: 7 }, async (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - i);
            date.setHours(0, 0, 0, 0);
            const nextDate = new Date(date);
            nextDate.setDate(nextDate.getDate() + 1);
            const count = await prisma.shift.count({
                where: {
                    startTime: {
                        gte: date,
                        lt: nextDate
                    },
                    ...(clientId && {
                        site: {
                            clientId: clientId
                        }
                    })
                }
            });
            return {
                date: date.toISOString().split('T')[0],
                shifts: count
            };
        }));
        const incidentsByPriority = await prisma.report.groupBy({
            by: ['priority'],
            where: {
                ...baseWhere,
                type: 'INCIDENT'
            },
            _count: {
                priority: true
            }
        });
        const topAgents = await prisma.agentProfile.findMany({
            where: {},
            include: {
                user: {
                    select: {
                        firstName: true,
                        lastName: true
                    }
                },
                shifts: {
                    where: {
                        ...baseWhere,
                        status: 'COMPLETED'
                    }
                },
                _count: {
                    select: {
                        shifts: {
                            where: {
                                ...baseWhere,
                                status: 'COMPLETED'
                            }
                        }
                    }
                }
            },
            orderBy: {
                shifts: {
                    _count: 'desc'
                }
            },
            take: 5
        });
        const recentActivities = await prisma.report.findMany({
            where: {
                createdAt: {
                    gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
                },
                ...(clientId && {
                    site: {
                        clientId: clientId
                    }
                })
            },
            include: {
                author: {
                    select: {
                        user: {
                            select: {
                                firstName: true,
                                lastName: true
                            }
                        }
                    }
                },
                site: {
                    select: {
                        name: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            },
            take: 10
        });
        const dashboardData = {
            overview: {
                activeShifts,
                totalAgents,
                incidentsToday,
                sitesMonitored,
                completionRate: Math.round(completionRate * 100) / 100,
                responseTime: Math.round(responseTime * 100) / 100
            },
            trends: {
                shifts: shiftTrends.reverse(),
                incidentsByPriority: incidentsByPriority.map(item => ({
                    priority: item.priority,
                    count: item._count.priority
                }))
            },
            topAgents: topAgents.map(agent => ({
                id: agent.id,
                name: `${agent.user.firstName} ${agent.user.lastName}`,
                completedShifts: 0,
                employeeId: agent.employeeId
            })),
            recentActivities: recentActivities.map(activity => ({
                id: activity.id,
                type: activity.type,
                title: activity.title,
                priority: activity.priority,
                agentName: `${activity.author.user.firstName} ${activity.author.user.lastName}`,
                siteName: activity.site?.name || 'Unknown Site',
                createdAt: activity.createdAt
            }))
        };
        res.json({
            success: true,
            data: dashboardData,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error fetching dashboard analytics:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch dashboard analytics'
            }
        });
    }
});
router.get('/performance', [
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601(),
    (0, express_validator_1.query)('agentId').optional().isUUID(),
    (0, express_validator_1.query)('siteId').optional().isUUID(),
], handleValidationErrors, async (req, res) => {
    try {
        const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date(new Date().setDate(new Date().getDate() - 30));
        const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
        const agentId = req.query.agentId;
        const siteId = req.query.siteId;
        const baseWhere = {
            createdAt: {
                gte: startDate,
                lte: endDate
            }
        };
        if (agentId)
            baseWhere.agentId = agentId;
        if (siteId)
            baseWhere.siteId = siteId;
        const shiftMetrics = await prisma.shift.groupBy({
            by: ['status'],
            where: baseWhere,
            _count: {
                status: true
            }
        });
        const reportMetrics = await prisma.report.groupBy({
            by: ['type', 'status'],
            where: baseWhere,
            _count: {
                type: true
            }
        });
        const attendanceMetrics = [];
        res.json({
            success: true,
            data: {
                shifts: shiftMetrics,
                reports: reportMetrics,
                attendance: attendanceMetrics,
                period: {
                    startDate,
                    endDate
                }
            }
        });
    }
    catch (error) {
        console.error('Error fetching performance metrics:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch performance metrics'
            }
        });
    }
});
router.get('/test-dashboard', async (req, res) => {
    console.log('Test dashboard endpoint hit - no auth required');
    try {
        const startDate = new Date(new Date().setDate(new Date().getDate() - 30));
        const endDate = new Date();
        const activeShifts = await prisma.shift.count({
            where: {
                status: 'IN_PROGRESS'
            }
        });
        const totalAgents = await prisma.agentProfile.count();
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const incidentsToday = await prisma.report.count({
            where: {
                type: 'INCIDENT',
                createdAt: {
                    gte: today,
                    lt: tomorrow
                }
            }
        });
        const sitesMonitored = await prisma.site.count({
            where: {
                status: 'ACTIVE'
            }
        });
        const dashboardData = {
            overview: {
                activeShifts,
                totalAgents,
                incidentsToday,
                sitesMonitored,
                completionRate: 85.5,
                responseTime: 12.3
            },
            message: 'Test endpoint working - authentication bypassed'
        };
        res.json({
            success: true,
            data: dashboardData,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error in test dashboard endpoint:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch test dashboard data'
            }
        });
    }
});
exports.default = router;
//# sourceMappingURL=analytics.js.map