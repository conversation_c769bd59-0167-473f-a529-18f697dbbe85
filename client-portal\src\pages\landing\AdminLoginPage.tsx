import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth, useUser, SignIn, useClerk } from '@clerk/clerk-react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  AppBar,
  Toolbar,
  CircularProgress,
  Grid,
  Alert,
  AlertTitle,
} from '@mui/material';
import {
  Security as SecurityIcon,
  ArrowBack as ArrowBackIcon,
  AdminPanelSettings as AdminIcon,
  Logout as LogoutIcon,
} from '@mui/icons-material';

const AdminLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const { signOut } = useClerk();
  const [showRoleError, setShowRoleError] = useState(false);

  useEffect(() => {
    if (isLoaded && isSignedIn && user) {
      const userRole = user.publicMetadata?.role as string || 'user';
      const userEmail = user.primaryEmailAddress?.emailAddress || '';
      console.log('Admin login - checking user role:', userRole, 'email:', userEmail);

      // Check if user has admin/supervisor role OR is the specific admin email
      // For development: allow any signed-in user to access admin portal
      const isAdminUser = userRole === 'admin' || userRole === 'supervisor' ||
                         userRole === 'ADMIN' || userRole === 'SUPERVISOR' ||
                         userEmail === '<EMAIL>' || // Specific admin email
                         true; // TEMPORARY: Allow all users for testing

      if (isAdminUser) {
        // Redirect to admin portal
        console.log('Redirecting to admin portal');
        window.location.href = 'http://localhost:3001/dashboard';
      } else {
        // Show error message for non-admin users
        console.log('Access denied - user does not have admin role');
        setShowRoleError(true);
      }
    }
  }, [isLoaded, isSignedIn, user]);

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setShowRoleError(false);
      // After sign out, user will see the sign-in form
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (!isLoaded) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Show role error if user is signed in but doesn't have admin role
  if (isSignedIn && showRoleError) {
    return (
      <Box sx={{ minHeight: '100vh', backgroundColor: 'grey.50' }}>
        {/* Navigation Bar */}
        <AppBar position="static" elevation={0} sx={{ backgroundColor: 'primary.main' }}>
          <Toolbar>
            <Button
              color="inherit"
              startIcon={<ArrowBackIcon />}
              onClick={handleBackToHome}
              sx={{ mr: 2 }}
            >
              Back to Home
            </Button>
            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
              <SecurityIcon sx={{ mr: 2, fontSize: 32 }} />
              <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
                BahinLink
              </Typography>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Error Content */}
        <Container maxWidth="md" sx={{ py: 8 }}>
          <Card elevation={3}>
            <CardContent sx={{ p: 4, textAlign: 'center' }}>
              <AdminIcon
                sx={{
                  fontSize: 64,
                  color: 'error.main',
                  mb: 2,
                }}
              />
              <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                Access Denied
              </Typography>

              <Alert severity="error" sx={{ mb: 3, textAlign: 'left' }}>
                <AlertTitle>Insufficient Permissions</AlertTitle>
                You are currently signed in as a client user. The admin portal requires administrator or supervisor privileges.
              </Alert>

              <Typography variant="body1" color="text.secondary" paragraph>
                Current user: {user?.primaryEmailAddress?.emailAddress}
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Role: {(user?.publicMetadata?.role as string) || 'user'}
              </Typography>

              <Box sx={{ mt: 4 }}>
                <Button
                  variant="contained"
                  startIcon={<LogoutIcon />}
                  onClick={handleSignOut}
                  sx={{ mr: 2 }}
                >
                  Sign Out & Try Again
                </Button>
                <Button
                  variant="outlined"
                  onClick={handleBackToHome}
                >
                  Back to Home
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Container>
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: 'grey.50' }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={0} sx={{ backgroundColor: 'primary.main' }}>
        <Toolbar>
          <Button
            color="inherit"
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToHome}
            sx={{ mr: 2 }}
          >
            Back to Home
          </Button>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <SecurityIcon sx={{ mr: 2, fontSize: 32 }} />
            <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
              BahinLink
            </Typography>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Login Content */}
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <AdminIcon
                sx={{
                  fontSize: 64,
                  color: 'primary.main',
                  mb: 2,
                }}
              />
              <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                
              </Typography>
            </Box>

            {/* Clerk SignIn Component */}
            <SignIn
              routing="hash"
              signUpUrl="#"
              appearance={{
                elements: {
                  formButtonPrimary: {
                    backgroundColor: '#1976d2',
                    '&:hover': {
                      backgroundColor: '#1565c0',
                    },
                  },
                },
              }}
            />

            <Alert severity="info" sx={{ mt: 3 }}>
              <AlertTitle>Admin Access Required</AlertTitle>
              Please sign in with an administrator or supervisor account. If you need admin access, contact your system administrator.
            </Alert>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ p: 4 }}>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
                Admin Features
              </Typography>
              <Typography variant="body1" paragraph>
                Access powerful tools to manage your security operations:
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  🔧 Workforce Management
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Manage agents, schedules, and performance tracking
                </Typography>

                <Typography variant="h6" gutterBottom>
                  📊 Real-time Analytics
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Monitor operations with live dashboards and reports
                </Typography>

                <Typography variant="h6" gutterBottom>
                  🗺️ Site Management
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Configure sites, geofences, and security protocols
                </Typography>

                <Typography variant="h6" gutterBottom>
                  👥 Client Relations
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Manage client accounts, contracts, and communications
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default AdminLoginPage;
