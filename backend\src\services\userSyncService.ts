import { PrismaClient } from '@prisma/client';
import { create<PERSON>lerkClient } from '@clerk/backend';

const prisma = new PrismaClient();
const clerkClient = createClerkClient({ secretKey: process.env.CLERK_SECRET_KEY });

export class UserSyncService {
  /**
   * Sync a single user from Clerk to database
   */
  static async syncUserFromClerk(clerkUserId: string): Promise<any> {
    try {
      // Get user from Clerk
      const clerkUser = await clerkClient.users.getUser(clerkUserId);
      
      if (!clerkUser) {
        throw new Error(`User not found in Clerk: ${clerkUserId}`);
      }

      // Check if user exists in database
      let dbUser = await prisma.user.findUnique({
        where: { clerkId: clerkUserId }
      });

      const userData = {
        clerkId: clerkUser.id,
        email: clerkUser.emailAddresses[0]?.emailAddress || '',
        firstName: clerkUser.firstName || '',
        lastName: clerkUser.lastName || '',
        username: clerkUser.username || clerkUser.emailAddresses[0]?.emailAddress?.split('@')[0],
        role: (clerkUser.publicMetadata?.role as any) || 'CLIENT',
        status: 'ACTIVE' as any,
        phone: clerkUser.phoneNumbers[0]?.phoneNumber || null,
      };

      if (dbUser) {
        // Update existing user
        dbUser = await prisma.user.update({
          where: { clerkId: clerkUserId },
          data: {
            ...userData,
            updatedAt: new Date(),
          }
        });
      } else {
        // Create new user
        dbUser = await prisma.user.create({
          data: userData
        });

        // Create role-specific profile
        await this.createUserProfile(dbUser);
      }

      return dbUser;
    } catch (error) {
      console.error('Error syncing user from Clerk:', error);
      throw error;
    }
  }

  /**
   * Create role-specific profile for user
   */
  static async createUserProfile(user: any): Promise<void> {
    try {
      switch (user.role) {
        case 'CLIENT':
          await prisma.clientProfile.create({
            data: {
              userId: user.id,
              companyName: '',
              contactPerson: `${user.firstName} ${user.lastName}`.trim(),
            }
          });
          break;

        case 'AGENT':
        case 'SUPERVISOR':
          await prisma.agentProfile.create({
            data: {
              userId: user.id,
              employeeId: `EMP-${Date.now()}`,
              hireDate: new Date(), // Required field
              // department: 'Security', // Field doesn't exist in schema
              skills: [],
              certifications: [],
            }
          });
          break;

        case 'ADMIN':
          await prisma.adminProfile.create({
            data: {
              userId: user.id,
              permissions: ['users.read', 'reports.read'],
              accessLevel: 'STANDARD' as any,
            }
          });
          break;
      }
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  /**
   * Sync all users from Clerk to database
   */
  static async syncAllUsersFromClerk(): Promise<{ synced: number; errors: number }> {
    let synced = 0;
    let errors = 0;
    let offset = 0;
    const limit = 100;

    try {
      while (true) {
        const usersResponse = await clerkClient.users.getUserList({
          limit,
          offset
        });

        const users = usersResponse.data || usersResponse;
        if (!users || users.length === 0) break;

        for (const clerkUser of users) {
          try {
            await this.syncUserFromClerk(clerkUser.id);
            synced++;
          } catch (error) {
            console.error(`Failed to sync user ${clerkUser.id}:`, error);
            errors++;
          }
        }

        offset += limit;
        
        // Break if we got fewer users than requested (last page)
        if (users.length < limit) break;
      }

      console.log(`User sync completed: ${synced} synced, ${errors} errors`);
      return { synced, errors };
    } catch (error) {
      console.error('Error during bulk user sync:', error);
      throw error;
    }
  }

  /**
   * Get user from database and sync with Clerk if needed
   */
  static async getUserWithSync(clerkUserId: string): Promise<any> {
    try {
      // Try to get user from database first
      let user = await prisma.user.findUnique({
        where: { clerkId: clerkUserId },
        include: {
          clientProfile: true,
          agentProfile: true,
          adminProfile: true,
        }
      });

      // If user doesn't exist in database, sync from Clerk
      if (!user) {
        console.log(`User ${clerkUserId} not found in database, syncing from Clerk...`);
        await this.syncUserFromClerk(clerkUserId);
        
        // Fetch the newly created user
        user = await prisma.user.findUnique({
          where: { clerkId: clerkUserId },
          include: {
            clientProfile: true,
            agentProfile: true,
            adminProfile: true,
          }
        });
      }

      return user;
    } catch (error) {
      console.error('Error getting user with sync:', error);
      throw error;
    }
  }

  /**
   * Create admin user manually in database and Clerk
   */
  static async createAdminUser(adminData: {
    email: string;
    firstName: string;
    lastName: string;
    password: string;
    permissions?: string[];
    accessLevel?: string;
  }): Promise<any> {
    try {
      // Create user in Clerk first
      const clerkUser = await clerkClient.users.createUser({
        emailAddress: [adminData.email],
        firstName: adminData.firstName,
        lastName: adminData.lastName,
        password: adminData.password,
        publicMetadata: {
          role: 'ADMIN',
          permissions: adminData.permissions || ['users.read', 'users.write', 'reports.read', 'reports.write'],
          accessLevel: adminData.accessLevel || 'FULL'
        }
      });

      // Create user in database
      const dbUser = await prisma.user.create({
        data: {
          clerkId: clerkUser.id,
          email: adminData.email,
          firstName: adminData.firstName,
          lastName: adminData.lastName,
          username: adminData.email.split('@')[0],
          role: 'ADMIN',
          status: 'ACTIVE',
        }
      });

      // Create admin profile
      await prisma.adminProfile.create({
        data: {
          userId: dbUser.id,
          permissions: adminData.permissions || ['users.read', 'users.write', 'reports.read', 'reports.write'],
          accessLevel: (adminData.accessLevel as any) || 'FULL',
        }
      });

      console.log('Admin user created successfully:', dbUser.id);
      return dbUser;
    } catch (error) {
      console.error('Error creating admin user:', error);
      throw error;
    }
  }
}
