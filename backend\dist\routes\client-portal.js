"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const express_validator_1 = require("express-validator");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'VALIDATION_ERROR',
                message: 'Invalid input data',
                details: errors.array()
            }
        });
    }
    next();
};
router.get('/dashboard', async (req, res) => {
    try {
        const clientId = 'mock-client-id';
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const sites = await prisma.site.findMany({
            where: {
                clientId: clientId,
            },
            select: {
                id: true,
                name: true,
                status: true
            }
        });
        const siteIds = sites.map(site => site.id);
        const activeShifts = await prisma.shift.count({
            where: {
                siteId: { in: siteIds },
                status: 'IN_PROGRESS',
            }
        });
        const incidentsToday = await prisma.report.count({
            where: {
                siteId: { in: siteIds },
                type: 'INCIDENT',
                createdAt: {
                    gte: today,
                    lt: tomorrow
                },
            }
        });
        const pendingRequests = await prisma.clientRequest.count({
            where: {
                clientId: clientId,
                status: {
                    in: ['PENDING', 'ACKNOWLEDGED', 'IN_PROGRESS']
                },
            }
        });
        const recentReports = await prisma.report.findMany({
            where: {
                siteId: { in: siteIds },
            },
            include: {
                author: {
                    select: {
                        user: {
                            select: {
                                firstName: true,
                                lastName: true
                            }
                        }
                    }
                },
                site: {
                    select: {
                        name: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            },
            take: 5
        });
        res.json({
            success: true,
            data: {
                overview: {
                    activeSites: sites.filter(site => site.status === 'ACTIVE').length,
                    activeShifts,
                    incidentsToday,
                    pendingRequests
                },
                recentReports: recentReports.map(report => ({
                    id: report.id,
                    type: report.type,
                    title: report.title,
                    status: report.status,
                    agentName: `${report.author.user.firstName} ${report.author.user.lastName}`,
                    siteName: report.site?.name || 'Unknown Site',
                    createdAt: report.createdAt
                }))
            }
        });
    }
    catch (error) {
        console.error('Error fetching client dashboard:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch dashboard data'
            }
        });
    }
});
router.get('/reports', [
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }).toInt(),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    (0, express_validator_1.query)('type').optional().isIn(['PATROL', 'INCIDENT', 'INSPECTION', 'MAINTENANCE', 'EMERGENCY']),
    (0, express_validator_1.query)('priority').optional().isIn(['LOW', 'NORMAL', 'HIGH', 'CRITICAL']),
    (0, express_validator_1.query)('siteId').optional().isUUID(),
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601(),
], handleValidationErrors, async (req, res) => {
    try {
        const clientId = 'mock-client-id';
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const type = req.query.type;
        const priority = req.query.priority;
        const siteId = req.query.siteId;
        const startDate = req.query.startDate;
        const endDate = req.query.endDate;
        const offset = (page - 1) * limit;
        const clientSites = await prisma.site.findMany({
            where: {
                clientId: clientId,
            },
            select: { id: true }
        });
        const siteIds = clientSites.map(site => site.id);
        const where = {
            siteId: { in: siteIds },
            status: {
                in: ['APPROVED', 'ARCHIVED']
            }
        };
        if (type)
            where.type = type;
        if (priority)
            where.priority = priority;
        if (siteId && siteIds.includes(siteId))
            where.siteId = siteId;
        if (startDate || endDate) {
            where.createdAt = {};
            if (startDate)
                where.createdAt.gte = new Date(startDate);
            if (endDate)
                where.createdAt.lte = new Date(endDate);
        }
        const [reports, total] = await Promise.all([
            prisma.report.findMany({
                where,
                skip: offset,
                take: limit,
                include: {
                    author: {
                        select: {
                            user: {
                                select: {
                                    firstName: true,
                                    lastName: true
                                }
                            }
                        }
                    },
                    site: {
                        select: {
                            name: true,
                            address: true
                        }
                    },
                },
                orderBy: { createdAt: 'desc' }
            }),
            prisma.report.count({ where })
        ]);
        res.json({
            success: true,
            data: {
                reports,
                pagination: {
                    page,
                    limit,
                    total,
                    pages: Math.ceil(total / limit)
                }
            }
        });
    }
    catch (error) {
        console.error('Error fetching client reports:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch reports'
            }
        });
    }
});
router.get('/service-requests', [
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }).toInt(),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    (0, express_validator_1.query)('status').optional().isIn(['OPEN', 'ASSIGNED', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'CANCELLED']),
    (0, express_validator_1.query)('type').optional().isIn(['ADDITIONAL_PATROL', 'EMERGENCY_RESPONSE', 'MAINTENANCE', 'CONSULTATION', 'OTHER']),
], handleValidationErrors, async (req, res) => {
    try {
        const clientId = 'mock-client-id';
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const status = req.query.status;
        const type = req.query.type;
        const offset = (page - 1) * limit;
        const where = {
            clientId: clientId,
        };
        if (status)
            where.status = status;
        if (type)
            where.type = type;
        const [requests, total] = await Promise.all([
            prisma.clientRequest.findMany({
                where,
                skip: offset,
                take: limit,
                include: {
                    site: {
                        select: {
                            name: true,
                            address: true
                        }
                    },
                    assignedTo: {
                        select: {
                            firstName: true,
                            lastName: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' }
            }),
            prisma.clientRequest.count({ where })
        ]);
        res.json({
            success: true,
            data: {
                requests,
                pagination: {
                    page,
                    limit,
                    total,
                    pages: Math.ceil(total / limit)
                }
            }
        });
    }
    catch (error) {
        console.error('Error fetching service requests:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch service requests'
            }
        });
    }
});
router.post('/service-requests', [
    (0, express_validator_1.body)('type').isIn(['ADDITIONAL_PATROL', 'EMERGENCY_RESPONSE', 'MAINTENANCE', 'CONSULTATION', 'OTHER']),
    (0, express_validator_1.body)('title').isString().isLength({ min: 1, max: 200 }).trim(),
    (0, express_validator_1.body)('description').isString().isLength({ min: 1, max: 2000 }).trim(),
    (0, express_validator_1.body)('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
    (0, express_validator_1.body)('siteId').optional().isUUID(),
    (0, express_validator_1.body)('urgentContact').optional().isObject(),
], handleValidationErrors, async (req, res) => {
    try {
        const clientId = 'mock-client-id';
        const { type, title, description, priority = 'MEDIUM', siteId, urgentContact } = req.body;
        if (siteId) {
            const site = await prisma.site.findFirst({
                where: {
                    id: siteId,
                    clientId: clientId,
                }
            });
            if (!site) {
                return res.status(404).json({
                    success: false,
                    error: {
                        code: 'SITE_NOT_FOUND',
                        message: 'Site not found or does not belong to client'
                    }
                });
            }
        }
        const request = await prisma.clientRequest.create({
            data: {
                clientId,
                type,
                title,
                description,
                priority,
                siteId,
                status: 'PENDING'
            },
            include: {
                site: {
                    select: {
                        name: true,
                        address: true
                    }
                }
            }
        });
        res.status(201).json({
            success: true,
            data: { request },
            message: 'Service request created successfully'
        });
    }
    catch (error) {
        console.error('Error creating service request:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to create service request'
            }
        });
    }
});
router.get('/sites', async (req, res) => {
    try {
        const clientId = 'mock-client-id';
        const sites = await prisma.site.findMany({
            where: {
                clientId: clientId,
            },
            include: {
                shifts: {
                    where: {
                        status: 'IN_PROGRESS',
                    },
                    include: {
                        agent: {
                            select: {
                                user: {
                                    select: {
                                        firstName: true,
                                        lastName: true
                                    }
                                }
                            }
                        }
                    }
                },
                _count: {
                    select: {
                        reports: {
                            where: {
                                createdAt: {
                                    gte: new Date(new Date().setDate(new Date().getDate() - 7))
                                }
                            }
                        }
                    }
                }
            },
            orderBy: { name: 'asc' }
        });
        res.json({
            success: true,
            data: { sites }
        });
    }
    catch (error) {
        console.error('Error fetching client sites:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch sites'
            }
        });
    }
});
exports.default = router;
//# sourceMappingURL=client-portal.js.map