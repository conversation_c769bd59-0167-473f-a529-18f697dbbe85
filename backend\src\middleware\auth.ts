import { Request, Response, NextFunction } from 'express';
import { createClerkClient } from '@clerk/backend';
import { UserSyncService } from '../services/userSyncService';
import { PrismaClient } from '@prisma/client';

// Extend Express Request type to include auth
declare global {
  namespace Express {
    interface Request {
      auth?: {
        userId: string;
        sessionId: string;
        claims: any;
      };
      user?: {
        id: string;
        role: string;
        email: string;
      };
    }
  }
}

// Initialize Clerk client and Prisma
const clerkClient = createClerkClient({ secretKey: process.env.CLERK_SECRET_KEY });
const prisma = new PrismaClient();

// Clerk authentication middleware
export const requireAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication token required'
        }
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Check if we have valid Clerk configuration
    const hasValidClerkConfig = process.env.CLERK_SECRET_KEY &&
                               process.env.CLERK_SECRET_KEY.startsWith('sk_') &&
                               process.env.CLERK_SECRET_KEY.length > 20;

    if (!hasValidClerkConfig) {
      console.warn('Invalid or missing Clerk configuration. Using fallback authentication.');

      // Fallback authentication for development/testing
      if (process.env.NODE_ENV === 'development') {
        // Try to find a user by email from the token (if it's an email)
        let user: any = null;

        // Check if token looks like an email
        if (token.includes('@')) {
          user = await prisma.user.findUnique({
            where: { email: token },
            include: {
              adminProfile: true,
              clientProfile: true,
              agentProfile: true
            }
          });
        }

        // If no user found by email, get the first admin user
        if (!user) {
          user = await prisma.user.findFirst({
            where: { role: 'ADMIN', status: 'ACTIVE' },
            include: {
              adminProfile: true,
              clientProfile: true,
              agentProfile: true
            }
          });
        }

        if (user) {
          req.auth = {
            userId: user.clerkId || user.id,
            sessionId: 'fallback-session',
            claims: { sub: user.clerkId || user.id }
          };

          req.user = {
            id: user.id,
            role: user.role,
            email: user.email
          };

          console.log(`Fallback auth: Authenticated user ${user.email} with role ${user.role}`);
          return next();
        }
      }

      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_CONFIGURATION_ERROR',
          message: 'Authentication service not properly configured'
        }
      });
    }

    // Verify Clerk JWT token
    let sessionClaims: any;
    let clerkUserId: string;

    try {
      // For development, let's use a simpler approach
      // Check if the token is a valid Clerk session token by trying to get user info
      try {
        // Try to use the token as a session token to get user info
        const user = await clerkClient.users.getUser(token);
        if (user && user.id) {
          clerkUserId = user.id;
          sessionClaims = { sub: user.id };
        } else {
          throw new Error('User not found with token');
        }
      } catch (userError) {
        console.log('Direct user lookup failed, trying session verification...');

        // If direct user lookup fails, the token might be a JWT
        // For now, let's use a fallback approach for development
        if (process.env.NODE_ENV === 'development') {
          // In development, if we can't verify the token, use the email fallback
          console.log('Using development fallback authentication');
          throw new Error('Fallback to development auth');
        } else {
          throw new Error('Token verification failed');
        }
      }

      if (!clerkUserId) {
        throw new Error('No user ID found in token');
      }

    } catch (error) {
      console.error('Clerk token verification failed:', error);
      console.error('Token received (first 20 chars):', token.substring(0, 20) + '...');

      // Fallback to development authentication if in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('Falling back to development authentication...');

        // Try to find a user by email from the token (if it's an email)
        let user: any = null;

        // Check if token looks like an email
        if (token.includes('@')) {
          user = await prisma.user.findUnique({
            where: { email: token },
            include: {
              adminProfile: true,
              clientProfile: true,
              agentProfile: true
            }
          });
        }

        // If no user found by email, get the first admin user
        if (!user) {
          user = await prisma.user.findFirst({
            where: { role: 'ADMIN', status: 'ACTIVE' },
            include: {
              adminProfile: true,
              clientProfile: true,
              agentProfile: true
            }
          });
        }

        if (user) {
          req.auth = {
            userId: user.clerkId || user.id,
            sessionId: 'fallback-session',
            claims: { sub: user.clerkId || user.id }
          };

          req.user = {
            id: user.id,
            role: user.role,
            email: user.email
          };

          console.log(`Fallback auth: Authenticated user ${user.email} with role ${user.role}`);
          return next();
        }
      }

      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid authentication token'
        }
      });
    }

    // Get user from database (sync from Clerk if needed)
    const user = await UserSyncService.getUserWithSync(clerkUserId);

    if (!user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    }

    // Add auth info to request
    req.auth = {
      userId: clerkUserId,
      sessionId: sessionClaims.sid || 'unknown',
      claims: sessionClaims
    };

    req.user = {
      id: user.id,
      role: user.role,
      email: user.email
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({
      success: false,
      error: {
        code: 'AUTHENTICATION_FAILED',
        message: 'Authentication failed'
      }
    });
  }
};

// Commented out Clerk auth for now
// export const requireAuth = ClerkExpressRequireAuth({
//   onError: (error) => {
//     console.error('Authentication error:', error);
//     return {
//       status: 401,
//       message: 'Authentication required'
//     };
//   }
// });

// Role-based authorization middleware
export const requireRole = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.auth || !req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required'
        }
      });
    }

    // Use the role from the database (already fetched by requireAuth)
    const userRole = req.user.role;

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: `You are currently signed in as a ${userRole.toLowerCase()} user. The admin portal requires administrator or supervisor privileges.`
        }
      });
    }

    next();
  };
};

// Admin only middleware
export const requireAdmin = requireRole(['ADMIN', 'SUPERVISOR']);

// Client only middleware
export const requireClient = requireRole(['CLIENT']);

// Agent middleware (includes supervisors and admins)
export const requireAgent = requireRole(['AGENT', 'SUPERVISOR', 'ADMIN']);

// Optional authentication middleware (doesn't require auth but adds user info if present)
export const optionalAuth = (req: Request, res: Response, next: NextFunction) => {
  // For development, we'll skip Clerk validation and use mock data
  if (process.env.NODE_ENV === 'development') {
    const mockUserId = req.headers['x-user-id'] as string || 'mock-user-id';
    const mockRole = req.headers['x-user-role'] as string || 'CLIENT';
    
    req.auth = {
      userId: mockUserId,
      sessionId: 'mock-session-id',
      claims: {
        email: '<EMAIL>'
      }
    };

    req.user = {
      id: mockUserId,
      role: mockRole,
      email: '<EMAIL>'
    };
  }

  next();
};

// Error handling middleware for authentication errors
export const handleAuthError = (error: any, req: Request, res: Response, next: NextFunction) => {
  if (error.name === 'UnauthorizedError' || error.status === 401) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'AUTHENTICATION_FAILED',
        message: 'Invalid or expired authentication token'
      }
    });
  }

  next(error);
};
