export declare class UserSyncService {
    static syncUserFromClerk(clerkUserId: string): Promise<any>;
    static createUserProfile(user: any): Promise<void>;
    static syncAllUsersFromClerk(): Promise<{
        synced: number;
        errors: number;
    }>;
    static getUserWithSync(clerkUserId: string): Promise<any>;
    static createAdminUser(adminData: {
        email: string;
        firstName: string;
        lastName: string;
        password: string;
        permissions?: string[];
        accessLevel?: string;
    }): Promise<any>;
}
//# sourceMappingURL=userSyncService.d.ts.map